from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from datetime import datetime
import json

from database import get_db
from models import User, Store, Order
from routers.auth import get_current_user

router = APIRouter()


class OrderResponse(BaseModel):
    id: int
    external_id: str
    order_number: str = None
    customer_email: str = None
    customer_id: str = None
    total_price: float = None
    subtotal_price: float = None
    total_tax: float = None
    total_discounts: float = None
    currency: str = "USD"
    status: str
    financial_status: str = None
    fulfillment_status: str = None
    order_date: datetime = None
    line_items: List[Dict[str, Any]] = []
    store_id: int

    class Config:
        from_attributes = True

    @classmethod
    def from_orm_with_line_items(cls, order):
        """Create OrderResponse with parsed line items"""
        line_items = []
        if order.line_items:
            try:
                line_items = json.loads(order.line_items) if isinstance(order.line_items, str) else order.line_items
            except (json.JSONDecodeError, TypeError):
                line_items = []

        return cls(
            id=order.id,
            external_id=order.external_id,
            order_number=order.order_number,
            customer_email=order.customer_email,
            customer_id=order.customer_id,
            total_price=order.total_price,
            subtotal_price=order.subtotal_price,
            total_tax=order.total_tax,
            total_discounts=order.total_discounts,
            currency=order.currency,
            status=order.status,
            financial_status=order.financial_status,
            fulfillment_status=order.fulfillment_status,
            order_date=order.order_date,
            line_items=line_items,
            store_id=order.store_id,
        )


@router.get("/", response_model=List[OrderResponse])
async def get_orders(
    store_id: Optional[int] = None, current_user: User = Depends(get_current_user), db: Session = Depends(get_db)
):
    query = db.query(Order).join(Store).filter(Store.owner_id == current_user.id)

    if store_id:
        query = query.filter(Order.store_id == store_id)

    orders = query.order_by(Order.order_date.desc()).all()
    return [OrderResponse.from_orm_with_line_items(order) for order in orders]


@router.get("/{order_id}", response_model=OrderResponse)
async def get_order(order_id: int, current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    order = db.query(Order).join(Store).filter(Order.id == order_id, Store.owner_id == current_user.id).first()

    if not order:
        raise HTTPException(status_code=404, detail="Order not found")

    return OrderResponse.from_orm_with_line_items(order)
