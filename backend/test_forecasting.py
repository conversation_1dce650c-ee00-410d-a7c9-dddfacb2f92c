#!/usr/bin/env python3
"""
Script to test forecasting functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import get_db
from services.forecasting_service import SalesForecastingService
from models import Product, SalesData
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """Main function to test forecasting"""
    print("🧪 Testing forecasting functionality...")
    
    # Get database session
    db = next(get_db())
    
    try:
        # Get first product
        product = db.query(Product).first()
        if not product:
            print("❌ No products found in database")
            return 1
            
        print(f"📦 Testing forecast for product: {product.title} (ID: {product.id})")
        
        # Check sales data
        sales_count = db.query(SalesData).filter(SalesData.product_id == product.id).count()
        print(f"📊 Sales data points for this product: {sales_count}")
        
        # Initialize forecasting service
        forecasting_service = SalesForecastingService(db)
        
        # Try to generate forecast
        result = forecasting_service.generate_forecast(product.id, days_ahead=30)
        
        if result["success"]:
            print(f"✅ Forecast generated successfully!")
            print(f"📈 Historical data points: {result['historical_data_points']}")
            print(f"🔮 Forecast period: {result['forecast_period_days']} days")
            print(f"📅 Generated at: {result['generated_at']}")
            
            # Show first few forecast points
            forecast_data = result.get("forecast_data", [])
            if forecast_data:
                print("\n📊 First 5 forecast points:")
                for i, point in enumerate(forecast_data[:5]):
                    print(f"  {point['date']}: {point['predicted_sales']:.2f} units")
        else:
            print(f"❌ Forecast failed: {result['message']}")
            if "suggestion" in result:
                print(f"💡 Suggestion: {result['suggestion']}")
            return 1
            
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return 1
    finally:
        db.close()
    
    print("🎉 Forecasting test completed!")
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
