#!/usr/bin/env python3
"""
Script to check order dates in the database
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import get_db
from models import Order
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """Main function to check order dates"""
    print("📅 Checking order dates...")
    
    # Get database session
    db = next(get_db())
    
    try:
        # Get all orders
        orders = db.query(Order).order_by(Order.order_date).all()
        
        print(f"📊 Total orders: {len(orders)}")
        
        if not orders:
            print("❌ No orders found in database")
            return 1
        
        # Check date distribution
        dates_with_none = 0
        unique_dates = set()
        
        print("\n📋 First 10 orders:")
        for i, order in enumerate(orders[:10]):
            print(f"  Order {order.id}: {order.order_date} (External ID: {order.external_id})")
            if order.order_date is None:
                dates_with_none += 1
            else:
                unique_dates.add(order.order_date.date())
        
        print(f"\n📈 Date Statistics:")
        print(f"Orders with None date: {dates_with_none}")
        print(f"Unique dates: {len(unique_dates)}")
        
        if unique_dates:
            print(f"Date range: {min(unique_dates)} to {max(unique_dates)}")
            print(f"Sample unique dates: {list(sorted(unique_dates))[:10]}")
        
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    finally:
        db.close()
    
    print("🎉 Order date check completed!")
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
