#!/usr/bin/env python3
"""
Script to debug sales data issues
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import get_db
from models import Product, SalesData
import pandas as pd
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """Main function to debug sales data"""
    print("🔍 Debugging sales data...")
    
    # Get database session
    db = next(get_db())
    
    try:
        # Get first product with sales data
        product = db.query(Product).first()
        if not product:
            print("❌ No products found in database")
            return 1
            
        print(f"📦 Checking product: {product.title} (ID: {product.id})")
        
        # Get sales data for this product
        sales_records = (
            db.query(SalesData)
            .filter(SalesData.product_id == product.id)
            .order_by(SalesData.date)
            .all()
        )
        
        print(f"📊 Total sales records: {len(sales_records)}")
        
        if not sales_records:
            print("❌ No sales data found for this product")
            return 1
        
        # Convert to DataFrame for analysis
        sales_data = [
            {
                "date": record.date,
                "quantity_sold": record.quantity_sold,
                "revenue": record.revenue
            }
            for record in sales_records
        ]
        
        df = pd.DataFrame(sales_data)
        print("\n📈 Sales Data Summary:")
        print(f"Date range: {df['date'].min()} to {df['date'].max()}")
        print(f"Quantity sold - Min: {df['quantity_sold'].min()}, Max: {df['quantity_sold'].max()}, Mean: {df['quantity_sold'].mean():.2f}")
        print(f"Revenue - Min: {df['revenue'].min()}, Max: {df['revenue'].max()}, Mean: {df['revenue'].mean():.2f}")
        
        # Check for NaN or invalid values
        print("\n🔍 Data Quality Check:")
        print(f"NaN in quantity_sold: {df['quantity_sold'].isna().sum()}")
        print(f"NaN in revenue: {df['revenue'].isna().sum()}")
        print(f"Zero quantity_sold: {(df['quantity_sold'] == 0).sum()}")
        print(f"Negative quantity_sold: {(df['quantity_sold'] < 0).sum()}")
        
        # Show first few records
        print("\n📋 First 10 records:")
        for i, record in enumerate(sales_records[:10]):
            print(f"  {record.date}: {record.quantity_sold} units, ${record.revenue:.2f}")
        
        # Prepare Prophet data format
        print("\n🔮 Prophet Data Preparation:")
        prophet_df = df.copy()
        prophet_df["ds"] = pd.to_datetime(prophet_df["date"])
        prophet_df["y"] = prophet_df["quantity_sold"]
        
        # Remove any rows with missing values
        original_len = len(prophet_df)
        prophet_df = prophet_df.dropna(subset=["ds", "y"])
        cleaned_len = len(prophet_df)
        
        print(f"Original records: {original_len}")
        print(f"After cleaning: {cleaned_len}")
        print(f"Removed records: {original_len - cleaned_len}")
        
        if cleaned_len > 0:
            print(f"Prophet y values - Min: {prophet_df['y'].min()}, Max: {prophet_df['y'].max()}, Mean: {prophet_df['y'].mean():.2f}")
            print(f"Prophet y values - Std: {prophet_df['y'].std():.2f}")
            
            # Check for constant values (which can cause issues)
            if prophet_df['y'].std() == 0:
                print("⚠️  WARNING: All y values are the same (constant), this may cause Prophet issues")
            
            # Check for very small values
            if prophet_df['y'].max() < 1e-10:
                print("⚠️  WARNING: Y values are very small, this may cause numerical issues")
        
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    finally:
        db.close()
    
    print("🎉 Sales data debugging completed!")
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
