#!/usr/bin/env python3
"""
Script to populate sales data from existing orders for forecasting
This script can be run manually to populate the SalesData table from Order data
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import get_db
from services.forecasting_service import SalesForecastingService
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """Main function to populate sales data"""
    print("🔄 Starting sales data population from existing orders...")
    
    # Get database session
    db = next(get_db())
    
    try:
        # Initialize forecasting service
        forecasting_service = SalesForecastingService(db)
        
        # Populate sales data
        result = forecasting_service.populate_sales_data_from_existing_orders()
        
        if result["success"]:
            print(f"✅ {result['message']}")
            print(f"📊 Total sales records: {result['total_sales_records']}")
        else:
            print(f"❌ Error: {result['message']}")
            return 1
            
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return 1
    finally:
        db.close()
    
    print("🎉 Sales data population completed!")
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
