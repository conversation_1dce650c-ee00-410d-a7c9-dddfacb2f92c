import React, { useState, useEffect } from "react";
import { Package, Search, Filter, Grid, List, X } from "lucide-react";
import { api } from "../services/api";
import ProductCard from "../components/ProductCard";
import ProductDetailModal from "../components/ProductDetailModal";
import SalesForecastChart from "../components/SalesForecastChart";

interface Product {
  id: number;
  external_id: string;
  title: string;
  description?: string;
  description_html?: string;
  handle?: string;
  price: number;
  compare_at_price?: number;
  inventory_quantity: number;
  sku?: string;
  barcode?: string;
  weight?: number;
  weight_unit?: string;
  status: string;
  product_type?: string;
  vendor?: string;
  tags?: string[];
  images?: string[];
  variants?: any[];
  store_id: number;
  order_count?: number; // <-- Add order count
}

interface Store {
  id: number;
  name: string;
  shop_name?: string;
  last_sync?: string; // Add last_sync to Store interface
}

interface ForecastData {
  date: string;
  predicted_sales: number;
  lower_bound: number;
  upper_bound: number;
}

interface ForecastResult {
  success: boolean;
  message?: string;
  forecast_data?: ForecastData[];
  product_info?: {
    id: number;
    title: string;
    sku?: string;
    current_inventory: number;
  };
}

const Products: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [stores, setStores] = useState<Store[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStore, setSelectedStore] = useState<number | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  // Modal states
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showForecastModal, setShowForecastModal] = useState(false);

  // Forecast states
  const [forecastData, setForecastData] = useState<ForecastResult | null>(null);
  const [forecastLoading, setForecastLoading] = useState(false);

  useEffect(() => {
    loadProducts();
    loadStores();
  }, []);

  const loadProducts = async () => {
    try {
      const response = await api.get("/api/products/", {
        params: selectedStore ? { store_id: selectedStore } : {},
      });
      setProducts(response.data);
    } catch (error) {
      console.error("Failed to load products:", error);
    } finally {
      setLoading(false);
    }
  };

  const loadStores = async () => {
    try {
      const response = await api.get("/api/stores/");
      setStores(response.data);
    } catch (error) {
      console.error("Failed to load stores:", error);
    }
  };

  const loadForecast = async (product: Product) => {
    setForecastLoading(true);
    try {
      const response = await api.get(`/api/products/${product.id}/forecast`);
      setForecastData(response.data);
    } catch (error) {
      console.error("Failed to load forecast:", error);
      setForecastData({
        success: false,
        message: "Failed to load forecast data",
      });
    } finally {
      setForecastLoading(false);
    }
  };

  const handleProductClick = async (product: Product) => {
    setSelectedProduct(product);
    setShowDetailModal(true);
    // Optionally, you could prefetch orders here if you want to avoid delay in modal
  };

  const handleForecastClick = async (product: Product) => {
    setSelectedProduct(product);
    setShowForecastModal(true);
    await loadForecast(product);
  };

  const handleRefreshForecast = async () => {
    if (selectedProduct) {
      await loadForecast(selectedProduct);
    }
  };

  const handlePopulateSalesData = async () => {
    try {
      setForecastLoading(true);
      const response = await api.post("/api/products/populate-sales-data");

      if (response.data.success) {
        // Show success message and refresh forecast
        alert(`Success: ${response.data.message}`);
        if (selectedProduct) {
          await loadForecast(selectedProduct);
        }
      } else {
        alert(`Error: ${response.data.message}`);
      }
    } catch (error) {
      console.error("Failed to populate sales data:", error);
      alert("Failed to populate sales data. Please try again.");
    } finally {
      setForecastLoading(false);
    }
  };

  const filteredProducts = products.filter((product) => {
    const matchesSearch =
      product.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.sku?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.vendor?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus =
      statusFilter === "all" || product.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Products</h1>
          <p className="text-gray-600">
            Manage your product catalog with interactive sales forecasting
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setViewMode("grid")}
            className={`p-2 rounded-lg ${
              viewMode === "grid"
                ? "bg-blue-500 text-white"
                : "bg-gray-100 text-gray-600"
            }`}
          >
            <Grid size={20} />
          </button>
          <button
            onClick={() => setViewMode("list")}
            className={`p-2 rounded-lg ${
              viewMode === "list"
                ? "bg-blue-500 text-white"
                : "bg-gray-100 text-gray-600"
            }`}
          >
            <List size={20} />
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Search */}
        <div className="relative">
          <Search
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
            size={20}
          />
          <input
            type="text"
            placeholder="Search products..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Store Filter */}
        <select
          value={selectedStore || ""}
          onChange={(e) =>
            setSelectedStore(e.target.value ? parseInt(e.target.value) : null)
          }
          className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="">All Stores</option>
          {stores.map((store) => (
            <option key={store.id} value={store.id}>
              {store.shop_name || store.name}
              {store.last_sync
                ? ` (Last sync: ${new Date(store.last_sync).toLocaleString()})`
                : ""}
            </option>
          ))}
        </select>

        {/* Status Filter */}
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="all">All Status</option>
          <option value="active">Active</option>
          <option value="draft">Draft</option>
          <option value="archived">Archived</option>
        </select>
      </div>

      {/* Products Display */}
      {filteredProducts.length > 0 ? (
        <div
          className={
            viewMode === "grid"
              ? "grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
              : "space-y-4"
          }
        >
          {filteredProducts.map((product) => (
            <ProductCard
              key={product.id}
              product={product}
              onClick={handleProductClick}
              onViewForecast={handleForecastClick}
              className={viewMode === "list" ? "flex-row" : ""}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <Package className="mx-auto text-gray-400 mb-4" size={64} />
          <h3 className="text-xl font-semibold text-gray-600 mb-2">
            {searchTerm ? "No products found" : "No products yet"}
          </h3>
          <p className="text-gray-500">
            {searchTerm
              ? "Try adjusting your search terms or filters"
              : "Connect a store and sync your products to see them here"}
          </p>
        </div>
      )}

      {/* Product Detail Modal */}
      {selectedProduct && (
        <ProductDetailModal
          product={selectedProduct}
          isOpen={showDetailModal}
          onClose={() => {
            setShowDetailModal(false);
            setSelectedProduct(null);
          }}
          onViewForecast={handleForecastClick}
        />
      )}

      {/* Forecast Modal */}
      {showForecastModal && selectedProduct && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between p-6 border-b">
              <h2 className="text-2xl font-bold text-gray-900">
                Sales Forecast - {selectedProduct.title}
              </h2>
              <button
                onClick={() => {
                  setShowForecastModal(false);
                  setSelectedProduct(null);
                  setForecastData(null);
                }}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X size={24} />
              </button>
            </div>
            <div className="p-6">
              {forecastData?.success &&
              forecastData.forecast_data &&
              forecastData.product_info ? (
                <SalesForecastChart
                  forecastData={forecastData.forecast_data}
                  productInfo={forecastData.product_info}
                  isLoading={forecastLoading}
                  onRefresh={handleRefreshForecast}
                  onPopulateSalesData={handlePopulateSalesData}
                />
              ) : (
                <SalesForecastChart
                  forecastData={[]}
                  productInfo={{
                    id: selectedProduct.id,
                    title: selectedProduct.title,
                    sku: selectedProduct.sku,
                    current_inventory: selectedProduct.inventory_quantity,
                  }}
                  isLoading={forecastLoading}
                  error={forecastData?.message}
                  onRefresh={handleRefreshForecast}
                  onPopulateSalesData={handlePopulateSalesData}
                />
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Products;
